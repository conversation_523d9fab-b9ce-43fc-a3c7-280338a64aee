
## Features

### Three-Page Dashboard Structure:

1. **Reporting Dashboard** - Historical emission data visualization and trends
2. **Prediction & Analysis** - Upload data, generate predictions with SHAP analysis
3. **DiCE Counterfactual** - Set reduction targets and get driver parameter recommendations

### 🔧 Key Capabilities:

- **Driver-Based Approach**: Predicts emission drivers first, then calculates final emissions
- **Multi-Scope Coverage**: Handles Scope 1, 2, and 3 emissions separately
- **Location-Specific**: Uses country-wise emission factors as multipliers
- **Model Interpretability**: SHAP analysis for understanding feature importance
- **Counterfactual Analysis**: DiCE-style recommendations for emission reduction
- **Time Series Predictions**: Generate predictions for custom date ranges

##  Project Structure

```
esg2/
├── app.py                          # Main Streamlit application
├── requirements.txt                # Python dependencies
├── test_system.py                 # System validation script
├── README.md                      # This file
├── data/                          # Data files
│   ├── scope1_train.csv          # Scope 1 training data
│   ├── scope2_train.csv          # Scope 2 training data
│   ├── scope3_train.csv          # Scope 3 training data
│   ├── combined_test_features.csv # Test/upload template
│   ├── emission_factors.csv      # Country/category emission factors
│   └── historical_emissions.csv  # Historical data for dashboard
├── models/                        # Trained model files (generated)
│   ├── scope1_models.pkl         # Scope 1 XGBoost models
│   ├── scope2_models.pkl         # Scope 2 XGBoost models
│   ├── scope3_models.pkl         # Scope 3 XGBoost models
│   ├── feature_maps.pkl          # Feature mapping configurations
│   └── scalers.pkl               # Data preprocessing scalers
└── utils/                         # Utility modules
    ├── __init__.py
    ├── data_preprocessing.py      # Data cleaning and feature engineering
    ├── model_training.py          # XGBoost model training pipeline
    ├── emission_calculator.py     # Driver-based emission calculations
    └── historical_data.py         # Historical data management
```

##  Installation & Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Train Models (First Time Setup)

Run the Streamlit app and use the "Train Models" button in the sidebar:

```bash
streamlit run app.py
```

Or train models programmatically:

```python
from utils.model_training import ESGModelTrainer

trainer = ESGModelTrainer()
models = trainer.train_all_models()
trainer.save_models()
```

### 3. Validate System (Optional)

```bash
python test_system.py
```

##  Usage Guide

### 1. Historical Dashboard
- View company-wide emission trends
- Analyze datacenter-wise breakdowns
- Explore scope distribution and patterns

### 2. Prediction & Analysis
- Upload CSV file with datacenter attributes
- Generate time-series emission predictions
- Analyze feature importance with SHAP
- Download prediction results

### 3. DiCE Counterfactual
- Set emission reduction targets by scope
- Get specific driver parameter recommendations
- Visualize impact of proposed changes

##  Data Format

### Upload CSV Format

See `data/combined_test_features.csv` for a complete example.

##  Model Architecture

### Driver-Based Approach:
1. **Feature Engineering**: Temporal features, holiday indicators, one-hot encoding
2. **Driver Prediction**: XGBoost models predict emission drivers for each scope
3. **Emission Calculation**: Apply country-specific emission factors to drivers
4. **Final Output**: Scope 1, 2, 3, and total emissions

### Scope Coverage:
- **Scope 1**: Direct emissions (diesel, natural gas, refrigerants, etc.)
- **Scope 2**: Electricity consumption with grid factors
- **Scope 3**: Value chain emissions (travel, waste, purchased goods)

##  Model Interpretability

### SHAP Analysis:
- **Summary Plots**: Overall feature importance across predictions
- **Waterfall Plots**: Individual prediction explanations
- **Feature Importance**: Ranked driver importance for each scope

##  Counterfactual Recommendations

### DiCE-Style Analysis:
- Set percentage reduction targets for each scope
- Get specific parameter change recommendations
- Visualize before/after emission scenarios
- Cascading driver adjustments for optimal results

Run `streamlit run app.py` to begin.
