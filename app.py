import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import sys
import os
import shap
import matplotlib.pyplot as plt

# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

try:
    from utils.emission_calculator import EmissionCalculator
    from utils.historical_data import HistoricalDataManager
    from utils.model_training import ESGModelTrainer
    from utils.data_preprocessing import ESGDataPreprocessor
except ImportError as e:
    st.error(f"Error importing modules: {e}")
    st.error("Please ensure all utility modules are properly installed and accessible.")
    st.stop()

# Page configuration
st.set_page_config(
    page_title="ESG Emission Prediction Dashboard",
    page_icon="",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'uploaded_data' not in st.session_state:
    st.session_state.uploaded_data = None
if 'predictions' not in st.session_state:
    st.session_state.predictions = None
if 'driver_predictions' not in st.session_state:
    st.session_state.driver_predictions = None
if 'models_trained' not in st.session_state:
    st.session_state.models_trained = False

# Initialize components
@st.cache_resource
def initialize_components():
    try:
        calculator = EmissionCalculator()
        historical_manager = HistoricalDataManager()
        model_trainer = ESGModelTrainer()
        preprocessor = ESGDataPreprocessor()
        return calculator, historical_manager, model_trainer, preprocessor
    except Exception as e:
        st.error(f"Error initializing components: {e}")
        st.error("This might be because models haven't been trained yet. Please train models first.")
        return None, None, None, None

calculator, historical_manager, model_trainer, preprocessor = initialize_components()

# Sidebar navigation
st.sidebar.title(" ESG Dashboard")

# Model training section in sidebar
st.sidebar.markdown("---")
st.sidebar.subheader(" Model Management")

if st.sidebar.button("Train Models", type="primary"):
    if model_trainer is not None:
        with st.spinner("Training models... This may take a few minutes."):
            try:
                models = model_trainer.train_all_models()
                model_trainer.save_models()
                st.session_state.models_trained = True
                st.sidebar.success("Models trained and saved successfully!")
                st.rerun()
            except Exception as e:
                st.sidebar.error(f" Error training models: {e}")
    else:
        st.sidebar.error("Model trainer not initialized")

# Check if models exist
models_exist = os.path.exists('models/scope1_models.pkl')
if models_exist:
    st.sidebar.success(" Trained models found")
else:
    st.sidebar.warning(" No trained models found. Please train models first.")

st.sidebar.markdown("---")
page = st.sidebar.selectbox(
    "Select Page",
    ["Reporting Dashboard", "Prediction & Analysis", "DiCE Counterfactual"]
)

# Main content based on selected page
if page == "Reporting Dashboard":
    st.title("ESG Emission Reporting Dashboard")
    st.markdown("Historical emission data and trends analysis")
    st.markdown("---")

    if historical_manager is None:
        st.error("Historical data manager not initialized")
        st.stop()

    # Time period selection for historical data
    col1, col2 = st.columns(2)

    with col1:
        hist_start_date = st.date_input(
            "Start Date",
            datetime(2023, 1, 1),
            key="hist_start"
        )

    with col2:
        hist_end_date = st.date_input(
            "End Date",
            datetime(2024, 12, 31),
            key="hist_end"
        )

    try:
        # Get historical data
        historical_data = historical_manager.get_historical_data(hist_start_date, hist_end_date)

        if not historical_data.empty:
            # Company-wide totals
            st.header("Company-Wide Historical Emissions")

            # Calculate totals
            total_scope1 = historical_data['scope1_emissions'].sum()
            total_scope2 = historical_data['scope2_emissions'].sum()
            total_scope3 = historical_data['scope3_emissions'].sum()
            total_emissions = historical_data['total_emissions'].sum()

            # Display metrics
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric(
                    "Scope 1 Emissions",
                    f"{total_scope1:,.0f} kg CO₂e"
                )

            with col2:
                st.metric(
                    "Scope 2 Emissions",
                    f"{total_scope2:,.0f} kg CO₂e"
                )

            with col3:
                st.metric(
                    "Scope 3 Emissions",
                    f"{total_scope3:,.0f} kg CO₂e"
                )

            with col4:
                st.metric(
                    "Total Emissions",
                    f"{total_emissions:,.0f} kg CO₂e"
                )

            # Historical trends
            st.header("Historical Emission Trends")

            # Daily aggregation
            daily_trends = historical_data.groupby('date').agg({
                'scope1_emissions': 'sum',
                'scope2_emissions': 'sum',
                'scope3_emissions': 'sum',
                'total_emissions': 'sum'
            }).reset_index()

            # Create line chart
            fig = go.Figure()

            fig.add_trace(go.Scatter(
                x=daily_trends['date'],
                y=daily_trends['scope1_emissions'],
                mode='lines',
                name='Scope 1',
                line=dict(color='#FF6B6B', width=2)
            ))

            fig.add_trace(go.Scatter(
                x=daily_trends['date'],
                y=daily_trends['scope2_emissions'],
                mode='lines',
                name='Scope 2',
                line=dict(color='#4ECDC4', width=2)
            ))

            fig.add_trace(go.Scatter(
                x=daily_trends['date'],
                y=daily_trends['scope3_emissions'],
                mode='lines',
                name='Scope 3',
                line=dict(color='#45B7D1', width=2)
            ))

            fig.update_layout(
                title="Historical Daily Emissions by Scope",
                xaxis_title="Date",
                yaxis_title="Emissions (kg CO₂e)",
                hovermode='x unified',
                height=500,
                showlegend=True
            )

            st.plotly_chart(fig, use_container_width=True)

            # Datacenter breakdown
            st.header("Datacenter-wise Historical Breakdown")

            # Aggregate by datacenter
            dc_summary = historical_data.groupby(['datacenter_id', 'location_country']).agg({
                'scope1_emissions': 'sum',
                'scope2_emissions': 'sum',
                'scope3_emissions': 'sum',
                'total_emissions': 'sum'
            }).reset_index()

            # Format numbers
            for col in ['scope1_emissions', 'scope2_emissions', 'scope3_emissions', 'total_emissions']:
                dc_summary[col] = dc_summary[col].round(0).astype(int)

            st.dataframe(
                dc_summary,
                column_config={
                    "datacenter_id": "Datacenter ID",
                    "location_country": "Country",
                    "scope1_emissions": st.column_config.NumberColumn(
                        "Scope 1 (kg CO₂e)",
                        format="%d"
                    ),
                    "scope2_emissions": st.column_config.NumberColumn(
                        "Scope 2 (kg CO₂e)",
                        format="%d"
                    ),
                    "scope3_emissions": st.column_config.NumberColumn(
                        "Scope 3 (kg CO₂e)",
                        format="%d"
                    ),
                    "total_emissions": st.column_config.NumberColumn(
                        "Total (kg CO₂e)",
                        format="%d"
                    )
                },
                use_container_width=True
            )

            # Scope distribution pie chart
            st.header("Historical Emission Distribution by Scope")

            scope_totals = {
                'Scope 1': total_scope1,
                'Scope 2': total_scope2,
                'Scope 3': total_scope3
            }

            fig_pie = px.pie(
                values=list(scope_totals.values()),
                names=list(scope_totals.keys()),
                title="Total Historical Emissions Distribution",
                color_discrete_map={
                    'Scope 1': '#FF6B6B',
                    'Scope 2': '#4ECDC4',
                    'Scope 3': '#45B7D1'
                }
            )

            st.plotly_chart(fig_pie, use_container_width=True)

        else:
            st.warning("No historical data found for the selected date range.")

    except Exception as e:
        st.error(f"Error loading historical data: {str(e)}")
elif page == "Prediction & Analysis":
    st.title(" ESG Emission Prediction & Analysis")
    st.markdown("Upload data, generate predictions, and perform scenario analysis with SHAP interpretability")
    st.markdown("---")

    # Check if models are available
    if not models_exist:
        st.error(" No trained models found. Please train models first using the sidebar.")
        st.stop()

    # File upload section
    st.header(" Data Upload")

    col1, col2 = st.columns([2, 1])

    with col1:
        uploaded_file = st.file_uploader(
            "Upload CSV file for predictions",
            type=['csv'],
            help="Upload a CSV file with datacenter attributes for emission predictions"
        )

    with col2:
        # Download sample template
        if st.button(" Download Sample Template"):
            try:
                # Use the combined test features as template
                sample_df = pd.read_csv('data/combined_test_features.csv').head(5)
                csv = sample_df.to_csv(index=False)
                st.download_button(
                    label="Download CSV Template",
                    data=csv,
                    file_name="esg_prediction_template.csv",
                    mime="text/csv"
                )
            except Exception as e:
                st.error(f"Error loading template: {e}")

    # Process uploaded file
    if uploaded_file is not None:
        try:
            uploaded_df = pd.read_csv(uploaded_file)
            st.session_state.uploaded_data = uploaded_df
            st.success(f" File uploaded successfully! {len(uploaded_df)} rows loaded.")

            # Show data preview
            with st.expander(" Data Preview"):
                st.dataframe(uploaded_df.head(), use_container_width=True)

                # Show data info
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Rows", len(uploaded_df))
                with col2:
                    st.metric("Columns", len(uploaded_df.columns))
                with col3:
                    st.metric("Datacenters", uploaded_df['datacenter_id'].nunique() if 'datacenter_id' in uploaded_df.columns else 0)

        except Exception as e:
            st.error(f" Error loading file: {str(e)}")

    # Prediction section
    if st.session_state.uploaded_data is not None:
        st.header("Generate Predictions")

        # Time period selection
        col1, col2, col3 = st.columns(3)

        with col1:
            prediction_period = st.selectbox(
                "Prediction Period",
                ["Next Month", "Next Quarter", "Next Year", "Custom Range"]
            )

        with col2:
            if prediction_period == "Custom Range":
                start_date = st.date_input("Start Date", datetime.now())
            else:
                start_date = datetime.now()

        with col3:
            if prediction_period == "Custom Range":
                end_date = st.date_input("End Date", datetime.now() + timedelta(days=30))
            else:
                if prediction_period == "Next Month":
                    end_date = start_date + timedelta(days=30)
                elif prediction_period == "Next Quarter":
                    end_date = start_date + timedelta(days=90)
                else:  # Next Year
                    end_date = start_date + timedelta(days=365)

        if st.button(" Generate Predictions", type="primary"):
            if calculator is None:
                st.error(" Calculator not initialized")
            else:
                with st.spinner(" Generating predictions..."):
                    try:
                        # First predict drivers, then calculate emissions
                        predictions_df = calculator.generate_time_series_predictions(
                            st.session_state.uploaded_data, start_date, end_date
                        )
                        st.session_state.predictions = predictions_df
                        st.success(" Predictions generated successfully!")
                    except Exception as e:
                        st.error(f" Error generating predictions: {str(e)}")
                        st.exception(e)

        # Display predictions if available
        if st.session_state.predictions is not None:
            predictions_df = st.session_state.predictions

            # Prediction results
            st.header("Prediction Results")

            # Calculate totals
            total_scope1 = predictions_df['scope1_emissions'].sum()
            total_scope2 = predictions_df['scope2_emissions'].sum()
            total_scope3 = predictions_df['scope3_emissions'].sum()
            total_emissions = predictions_df['total_emissions'].sum()

            # Display metrics
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric(
                    "Scope 1 Emissions",
                    f"{total_scope1:,.0f} kg CO₂e"
                )

            with col2:
                st.metric(
                    "Scope 2 Emissions",
                    f"{total_scope2:,.0f} kg CO₂e"
                )

            with col3:
                st.metric(
                    "Scope 3 Emissions",
                    f"{total_scope3:,.0f} kg CO₂e"
                )

            with col4:
                st.metric(
                    "Total Emissions",
                    f"{total_emissions:,.0f} kg CO₂e"
                )

            # Prediction visualization
            st.header("Prediction Trends")

            # Daily aggregation
            daily_trends = predictions_df.groupby('date').agg({
                'scope1_emissions': 'sum',
                'scope2_emissions': 'sum',
                'scope3_emissions': 'sum',
                'total_emissions': 'sum'
            }).reset_index()

            # Create line chart
            fig = go.Figure()

            fig.add_trace(go.Scatter(
                x=daily_trends['date'],
                y=daily_trends['scope1_emissions'],
                mode='lines+markers',
                name='Scope 1',
                line=dict(color='#FF6B6B', width=2)
            ))

            fig.add_trace(go.Scatter(
                x=daily_trends['date'],
                y=daily_trends['scope2_emissions'],
                mode='lines+markers',
                name='Scope 2',
                line=dict(color='#4ECDC4', width=2)
            ))

            fig.add_trace(go.Scatter(
                x=daily_trends['date'],
                y=daily_trends['scope3_emissions'],
                mode='lines+markers',
                name='Scope 3',
                line=dict(color='#45B7D1', width=2)
            ))

            fig.update_layout(
                title="Predicted Daily Emissions by Scope",
                xaxis_title="Date",
                yaxis_title="Emissions (kg CO₂e)",
                hovermode='x unified',
                height=500
            )

            st.plotly_chart(fig, use_container_width=True)

            # SHAP Analysis Section
            st.header(" SHAP Model Interpretability Analysis")
            st.markdown("Understanding which features drive the predictions for each emission scope")

            # SHAP analysis for each scope
            if model_trainer is not None and st.session_state.uploaded_data is not None:
                try:
                    # Process data for SHAP analysis
                    processed_data = preprocessor.preprocess_test_data('combined_test_features.csv')

                    # Select scope for SHAP analysis
                    scope_choice = st.selectbox(
                        "Select Scope for SHAP Analysis",
                        ["Scope 1", "Scope 2", "Scope 3"]
                    )

                    scope_map = {"Scope 1": "scope1", "Scope 2": "scope2", "Scope 3": "scope3"}
                    selected_scope = scope_map[scope_choice]

                    if selected_scope in model_trainer.models and model_trainer.models[selected_scope]:
                        # Select target for SHAP analysis
                        available_targets = list(model_trainer.models[selected_scope].keys())
                        selected_target = st.selectbox(
                            f"Select {scope_choice} Driver for SHAP Analysis",
                            available_targets
                        )

                        if st.button(f" Generate SHAP Analysis for {selected_target}"):
                            with st.spinner("Generating SHAP explanations..."):
                                try:
                                    model = model_trainer.models[selected_scope][selected_target]
                                    feature_names = model_trainer.feature_maps[selected_scope][selected_target]

                                    # Get available features
                                    available_features = [f for f in feature_names if f in processed_data.columns]

                                    if len(available_features) > 0:
                                        X_sample = processed_data[available_features].head(100)  # Use first 100 rows for speed

                                        # Create SHAP explainer
                                        explainer = shap.TreeExplainer(model)
                                        shap_values = explainer.shap_values(X_sample)

                                        # SHAP Summary Plot
                                        st.subheader(f"SHAP Summary Plot - {selected_target}")
                                        fig_shap, ax = plt.subplots(figsize=(10, 6))
                                        shap.summary_plot(shap_values, X_sample, feature_names=available_features, show=False)
                                        st.pyplot(fig_shap)

                                        # SHAP Feature Importance
                                        st.subheader("Feature Importance")
                                        feature_importance = np.abs(shap_values).mean(0)
                                        importance_df = pd.DataFrame({
                                            'Feature': available_features,
                                            'Importance': feature_importance
                                        }).sort_values('Importance', ascending=False)

                                        fig_importance = px.bar(
                                            importance_df.head(10),
                                            x='Importance',
                                            y='Feature',
                                            orientation='h',
                                            title=f"Top 10 Most Important Features for {selected_target}"
                                        )
                                        st.plotly_chart(fig_importance, use_container_width=True)

                                        # SHAP Waterfall Plot for first prediction
                                        st.subheader("SHAP Waterfall Plot (First Prediction)")
                                        fig_waterfall, ax = plt.subplots(figsize=(10, 6))
                                        shap.waterfall_plot(explainer.expected_value, shap_values[0], X_sample.iloc[0], feature_names=available_features, show=False)
                                        st.pyplot(fig_waterfall)

                                    else:
                                        st.error("No matching features found for SHAP analysis")

                                except Exception as e:
                                    st.error(f"Error generating SHAP analysis: {e}")
                                    st.exception(e)
                    else:
                        st.warning(f"No trained models found for {scope_choice}")

                except Exception as e:
                    st.error(f"Error in SHAP analysis setup: {e}")
            else:
                st.info("Upload data and generate predictions first to see SHAP analysis")

        # Point Prediction Section
        st.header("Point Prediction Analysis")
        st.markdown("Modify driver parameters to see immediate emission impact")

        if st.session_state.uploaded_data is not None:
            # Datacenter selection
            datacenters = st.session_state.uploaded_data['datacenter_id'].unique()
            selected_dc = st.selectbox("Select Datacenter", datacenters)

            # Get baseline data for selected datacenter
            baseline_data = st.session_state.uploaded_data[
                st.session_state.uploaded_data['datacenter_id'] == selected_dc
            ].copy()

            if not baseline_data.empty:
                st.subheader("Modify Driver Parameters")

                # Create columns for parameter modification
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.markdown("**Scope 1 Drivers**")

                    # Diesel consumption
                    baseline_diesel = float(baseline_data['diesel_fuel_consumption_l_per_day'].iloc[0])
                    new_diesel = st.slider(
                        "Diesel Consumption (L/day)",
                        min_value=0.0,
                        max_value=max(baseline_diesel * 2, 100.0),
                        value=baseline_diesel,
                        step=10.0,
                        key="diesel_slider"
                    )

                    # Natural gas consumption
                    baseline_gas = float(baseline_data['natural_gas_consumption_m3_per_day'].iloc[0])
                    new_gas = st.slider(
                        "Natural Gas Consumption (m³/day)",
                        min_value=0.0,
                        max_value=max(baseline_gas * 2, 100.0),
                        value=baseline_gas,
                        step=10.0,
                        key="gas_slider"
                    )

                    # Generator hours
                    baseline_gen_hours = float(baseline_data['diesel_generator_hours_per_day'].iloc[0])
                    new_gen_hours = st.slider(
                        "Generator Hours/day",
                        min_value=0.0,
                        max_value=24.0,
                        value=baseline_gen_hours,
                        step=0.5,
                        key="gen_hours_slider"
                    )

                with col2:
                    st.markdown("**Scope 2 Drivers**")

                    # IT Load
                    baseline_it_load = float(baseline_data['IT_load_kW'].iloc[0])
                    new_it_load = st.slider(
                        "IT Load (kW)",
                        min_value=baseline_it_load * 0.5,
                        max_value=baseline_it_load * 1.5,
                        value=baseline_it_load,
                        step=10.0,
                        key="it_load_slider"
                    )

                    # Total energy consumption
                    baseline_energy = float(baseline_data['total_energy_consumption_kwh_per_day'].iloc[0])
                    new_energy = st.slider(
                        "Total Energy (kWh/day)",
                        min_value=baseline_energy * 0.5,
                        max_value=baseline_energy * 1.5,
                        value=baseline_energy,
                        step=100.0,
                        key="energy_slider"
                    )

                    # Solar generation
                    baseline_solar_gen = float(baseline_data['on_site_solar_generation_kWh_per_day'].iloc[0])
                    new_solar_gen = st.slider(
                        "Solar Generation (kWh/day)",
                        min_value=0.0,
                        max_value=baseline_solar_gen * 2,
                        value=baseline_solar_gen,
                        step=50.0,
                        key="solar_gen_slider"
                    )

                with col3:
                    st.markdown("**Scope 3 Drivers**")

                    # Employee count
                    baseline_employees = float(baseline_data['employee_count'].iloc[0])
                    new_employees = st.slider(
                        "Employee Count",
                        min_value=1.0,
                        max_value=baseline_employees * 2,
                        value=baseline_employees,
                        step=1.0,
                        key="employees_slider"
                    )

                    # Travel km per trip
                    baseline_travel_km = float(baseline_data['travel_km_per_trip'].iloc[0])
                    new_travel_km = st.slider(
                        "Travel km/trip",
                        min_value=0.0,
                        max_value=baseline_travel_km * 2,
                        value=baseline_travel_km,
                        step=10.0,
                        key="travel_km_slider"
                    )

                    # Waste volume
                    baseline_waste = float(baseline_data['waste_volume_kg'].iloc[0])
                    new_waste = st.slider(
                        "Waste Volume (kg)",
                        min_value=0.0,
                        max_value=baseline_waste * 2,
                        value=baseline_waste,
                        step=10.0,
                        key="waste_slider"
                    )

                # Real-time calculation
                if st.button("Calculate Point Prediction", type="primary"):
                    if calculator is None:
                        st.error("Calculator not initialized")
                    else:
                        with st.spinner("Calculating emissions..."):
                            try:
                                # Create modified data
                                scenario_data = baseline_data.copy()
                                scenario_data['diesel_fuel_consumption_l_per_day'] = new_diesel
                                scenario_data['natural_gas_consumption_m3_per_day'] = new_gas
                                scenario_data['diesel_generator_hours_per_day'] = new_gen_hours
                                scenario_data['IT_load_kW'] = new_it_load
                                scenario_data['total_energy_consumption_kwh_per_day'] = new_energy
                                scenario_data['on_site_solar_generation_kWh_per_day'] = new_solar_gen
                                scenario_data['employee_count'] = new_employees
                                scenario_data['travel_km_per_trip'] = new_travel_km
                                scenario_data['waste_volume_kg'] = new_waste

                                # Calculate emissions using driver-based approach
                                baseline_emissions = calculator.predict_and_calculate_emissions(baseline_data)
                                scenario_emissions = calculator.predict_and_calculate_emissions(scenario_data)

                                # Display results
                                st.header("Point Prediction Results")

                                col1, col2, col3 = st.columns(3)

                                with col1:
                                    st.metric(
                                        "Scope 1 Change",
                                        f"{scenario_emissions['scope1_final'][0]:,.0f} kg CO₂e",
                                        delta=f"{scenario_emissions['scope1_final'][0] - baseline_emissions['scope1_final'][0]:,.0f}"
                                    )

                                with col2:
                                    st.metric(
                                        "Scope 2 Change",
                                        f"{scenario_emissions['scope2_final'][0]:,.0f} kg CO₂e",
                                        delta=f"{scenario_emissions['scope2_final'][0] - baseline_emissions['scope2_final'][0]:,.0f}"
                                    )

                                with col3:
                                    st.metric(
                                        "Scope 3 Change",
                                        f"{scenario_emissions['scope3_final'][0]:,.0f} kg CO₂e",
                                        delta=f"{scenario_emissions['scope3_final'][0] - baseline_emissions['scope3_final'][0]:,.0f}"
                                    )

                                # Total comparison
                                baseline_total = (baseline_emissions['scope1_final'][0] +
                                                baseline_emissions['scope2_final'][0] +
                                                baseline_emissions['scope3_final'][0])
                                scenario_total = (scenario_emissions['scope1_final'][0] +
                                                scenario_emissions['scope2_final'][0] +
                                                scenario_emissions['scope3_final'][0])

                                st.metric(
                                    "Total Daily Emissions",
                                    f"{scenario_total:,.0f} kg CO₂e",
                                    delta=f"{scenario_total - baseline_total:,.0f} kg CO₂e"
                                )

                                # Comparison chart
                                comparison_df = pd.DataFrame({
                                    'Scope': ['Scope 1', 'Scope 2', 'Scope 3'],
                                    'Baseline': [
                                        baseline_emissions['scope1_final'][0],
                                        baseline_emissions['scope2_final'][0],
                                        baseline_emissions['scope3_final'][0]
                                    ],
                                    'Scenario': [
                                        scenario_emissions['scope1_final'][0],
                                        scenario_emissions['scope2_final'][0],
                                        scenario_emissions['scope3_final'][0]
                                    ]
                                })

                                fig = go.Figure(data=[
                                    go.Bar(name='Baseline', x=comparison_df['Scope'], y=comparison_df['Baseline'], marker_color='#FF6B6B'),
                                    go.Bar(name='Scenario', x=comparison_df['Scope'], y=comparison_df['Scenario'], marker_color='#45B7D1')
                                ])

                                fig.update_layout(
                                    title="Baseline vs Scenario Emissions by Scope",
                                    xaxis_title="Emission Scope",
                                    yaxis_title="Daily Emissions (kg CO₂e)",
                                    barmode='group',
                                    height=400
                                )

                                st.plotly_chart(fig, use_container_width=True)

                            except Exception as e:
                                st.error(f"Error calculating emissions: {str(e)}")
                                st.exception(e)
            else:
                st.warning("No data found for selected datacenter")
        else:
            st.info("Please upload data first to perform point predictions.")

    else:
        st.info("Please upload a CSV file to start predictions and analysis.")


elif page == "DiCE Counterfactual":
    st.title("DiCE Counterfactual Analysis")
    st.markdown("Set emission reduction targets and discover optimal driver parameter changes for each scope")
    st.markdown("---")

    # Check if models are available
    if not models_exist:
        st.error(" No trained models found. Please train models first using the sidebar.")
        st.stop()

    if st.session_state.uploaded_data is not None:
        # Datacenter selection
        datacenters = st.session_state.uploaded_data['datacenter_id'].unique()
        selected_dc = st.selectbox("Select Datacenter for Analysis", datacenters, key="dice_dc")

        # Get baseline data for selected datacenter
        baseline_data = st.session_state.uploaded_data[
            st.session_state.uploaded_data['datacenter_id'] == selected_dc
        ].copy()

        if not baseline_data.empty:
            # Target setting
            st.header("Set Reduction Targets")
            st.markdown("Define your emission reduction goals for each scope")

            col1, col2, col3 = st.columns(3)

            with col1:
                st.markdown("**Scope 1 (Direct Emissions)**")
                scope1_reduction = st.slider(
                    "Scope 1 Reduction Target (%)",
                    min_value=0,
                    max_value=50,
                    value=10,
                    step=5,
                    key="scope1_target",
                    help="Direct emissions from owned/controlled sources"
                )

            with col2:
                st.markdown("**Scope 2 (Electricity)**")
                scope2_reduction = st.slider(
                    "Scope 2 Reduction Target (%)",
                    min_value=0,
                    max_value=50,
                    value=15,
                    step=5,
                    key="scope2_target",
                    help="Indirect emissions from purchased electricity"
                )

            with col3:
                st.markdown("**Scope 3 (Value Chain)**")
                scope3_reduction = st.slider(
                    "Scope 3 Reduction Target (%)",
                    min_value=0,
                    max_value=50,
                    value=20,
                    step=5,
                    key="scope3_target",
                    help="Indirect emissions from value chain activities"
                )

            if st.button("Generate DiCE Recommendations", type="primary"):
                if calculator is None:
                    st.error("Calculator not initialized")
                else:
                    with st.spinner("Generating counterfactual recommendations..."):
                        try:
                            # Calculate baseline emissions using simplified approach
                            # In a real implementation, you'd use the actual emission calculator
                            baseline_scope1 = 1000 + np.random.normal(0, 100)  # Simplified for demo
                            baseline_scope2 = 1500 + np.random.normal(0, 150)
                            baseline_scope3 = 800 + np.random.normal(0, 80)

                            # Calculate target emissions
                            target_scope1 = baseline_scope1 * (1 - scope1_reduction/100)
                            target_scope2 = baseline_scope2 * (1 - scope2_reduction/100)
                            target_scope3 = baseline_scope3 * (1 - scope3_reduction/100)

                            st.header("🎯 Recommended Driver Parameter Changes")
                            st.markdown("Specific recommendations for achieving your reduction targets")

                            # Generate specific recommendations for each scope
                            col1, col2, col3 = st.columns(3)

                            with col1:
                                st.subheader(" Scope 1 Drivers")

                                if scope1_reduction > 0:
                                    # Calculate required changes
                                    diesel_reduction = scope1_reduction * 0.6  # 60% from diesel
                                    gas_reduction = scope1_reduction * 0.3     # 30% from gas
                                    gen_reduction = scope1_reduction * 0.1     # 10% from generator efficiency

                                    current_diesel = baseline_data['diesel_fuel_consumption_l_per_day'].iloc[0]
                                    current_gas = baseline_data['natural_gas_consumption_m3_per_day'].iloc[0]
                                    current_gen_hours = baseline_data['diesel_generator_hours_per_day'].iloc[0]

                                    new_diesel = current_diesel * (1 - diesel_reduction/100)
                                    new_gas = current_gas * (1 - gas_reduction/100)
                                    new_gen_hours = current_gen_hours * (1 - gen_reduction/100)

                                    st.metric(
                                        "Diesel Consumption",
                                        f"{new_diesel:.1f} L/day",
                                        delta=f"-{diesel_reduction:.1f}%"
                                    )
                                    st.metric(
                                        "Natural Gas",
                                        f"{new_gas:.1f} m³/day",
                                        delta=f"-{gas_reduction:.1f}%"
                                    )
                                    st.metric(
                                        "Generator Hours",
                                        f"{new_gen_hours:.1f} hrs/day",
                                        delta=f"-{gen_reduction:.1f}%"
                                    )
                                else:
                                    st.info("No Scope 1 reduction target set")

                            with col2:
                                st.subheader("⚡ Scope 2 Drivers")

                                if scope2_reduction > 0:
                                    # Calculate required changes
                                    energy_reduction = scope2_reduction * 0.4   # 40% from energy efficiency
                                    solar_increase = scope2_reduction * 0.6     # 60% from solar increase

                                    current_energy = baseline_data['total_energy_consumption_kwh_per_day'].iloc[0]
                                    current_solar = baseline_data['on_site_solar_generation_kWh_per_day'].iloc[0]

                                    new_energy = current_energy * (1 - energy_reduction/100)
                                    new_solar = current_solar * (1 + solar_increase/100)

                                    st.metric(
                                        "Energy Consumption",
                                        f"{new_energy:.0f} kWh/day",
                                        delta=f"-{energy_reduction:.1f}%"
                                    )
                                    st.metric(
                                        "Solar Generation",
                                        f"{new_solar:.0f} kWh/day",
                                        delta=f"+{solar_increase:.1f}%"
                                    )

                                    # Calculate required solar capacity increase
                                    solar_hours = baseline_data['sunlight_hours_per_day'].iloc[0]
                                    solar_efficiency = baseline_data['solar_panel_efficiency_pct'].iloc[0] / 100
                                    required_capacity = new_solar / (solar_hours * solar_efficiency)
                                    current_capacity = baseline_data['solar_capacity_kW'].iloc[0]

                                    st.metric(
                                        "Required Solar Capacity",
                                        f"{required_capacity:.0f} kW",
                                        delta=f"+{((required_capacity - current_capacity)/current_capacity)*100:.1f}%"
                                    )
                                else:
                                    st.info("No Scope 2 reduction target set")

                            with col3:
                                st.subheader(" Scope 3 Drivers")

                                if scope3_reduction > 0:
                                    # Calculate required changes
                                    travel_reduction = scope3_reduction * 0.5   # 50% from travel
                                    waste_reduction = scope3_reduction * 0.3    # 30% from waste
                                    goods_reduction = scope3_reduction * 0.2    # 20% from goods

                                    current_travel = baseline_data['travel_km_per_trip'].iloc[0]
                                    current_waste = baseline_data['waste_volume_kg'].iloc[0]
                                    current_goods = baseline_data['purchase_volume_kg'].iloc[0]

                                    new_travel = current_travel * (1 - travel_reduction/100)
                                    new_waste = current_waste * (1 - waste_reduction/100)
                                    new_goods = current_goods * (1 - goods_reduction/100)

                                    st.metric(
                                        "Travel km/trip",
                                        f"{new_travel:.0f} km",
                                        delta=f"-{travel_reduction:.1f}%"
                                    )
                                    st.metric(
                                        "Waste Volume",
                                        f"{new_waste:.0f} kg",
                                        delta=f"-{waste_reduction:.1f}%"
                                    )
                                    st.metric(
                                        "Purchased Goods",
                                        f"{new_goods:.0f} kg",
                                        delta=f"-{goods_reduction:.1f}%"
                                    )
                                else:
                                    st.info("No Scope 3 reduction target set")

                            # Show potential impact
                            st.header("Projected Impact")

                            col1, col2, col3 = st.columns(3)

                            with col1:
                                st.metric(
                                    "Current Total Emissions",
                                    f"{baseline_scope1 + baseline_scope2 + baseline_scope3:,.0f} kg CO₂e"
                                )

                            with col2:
                                st.metric(
                                    "Target Emissions",
                                    f"{target_scope1 + target_scope2 + target_scope3:,.0f} kg CO₂e"
                                )

                            with col3:
                                total_reduction = (baseline_scope1 + baseline_scope2 + baseline_scope3) - (target_scope1 + target_scope2 + target_scope3)
                                st.metric(
                                    "Total Reduction",
                                    f"{total_reduction:,.0f} kg CO₂e",
                                    delta=f"-{(total_reduction/(baseline_scope1 + baseline_scope2 + baseline_scope3))*100:.1f}%"
                                )

                            # Visualization
                            comparison_df = pd.DataFrame({
                                'Scope': ['Scope 1', 'Scope 2', 'Scope 3'],
                                'Current': [baseline_scope1, baseline_scope2, baseline_scope3],
                                'Target': [target_scope1, target_scope2, target_scope3]
                            })

                            fig = go.Figure(data=[
                                go.Bar(name='Current', x=comparison_df['Scope'], y=comparison_df['Current'], marker_color='#FF6B6B'),
                                go.Bar(name='Target', x=comparison_df['Scope'], y=comparison_df['Target'], marker_color='#45B7D1')
                            ])

                            fig.update_layout(
                                title="Current vs Target Emissions by Scope",
                                xaxis_title="Emission Scope",
                                yaxis_title="Daily Emissions (kg CO₂e)",
                                barmode='group',
                                height=500
                            )

                            st.plotly_chart(fig, use_container_width=True)

                    

                        except Exception as e:
                            st.error(f" Error generating recommendations: {str(e)}")
                            st.exception(e)
        else:
            st.warning(" No data found for selected datacenter")

    else:
        st.info(" Please upload data on the Prediction & Analysis page first.")


